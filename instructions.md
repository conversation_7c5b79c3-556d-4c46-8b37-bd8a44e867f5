You are an expert Java developer specializing in modern, clean Spring Boot applications. Your task is to create a backend-only REST API project from scratch based on the following detailed requirements.

**Project Goal:**
Create a REST API that accepts start and end coordinates, calls the Openrouteservice (ORS) API to get routing information, and returns a structured JSON response containing the ETA, distance, and turn-by-turn directions.

**Core Requirements:**
1.  **No Frontend:** This is a pure backend service.
2.  **Configuration:** The Openrouteservice API Key must be configurable in the `application.properties` file.
3.  **API Endpoint:** Expose a single GET endpoint.
4.  **Data Modeling:** Use Data Transfer Objects (DTOs) for clean, type-safe handling of both the external ORS response and our API's response.
5.  **Clean Architecture:** The code should be well-structured into controller, service, and DTO packages.

