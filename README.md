# OSM POC - Route Planning API

A Spring Boot REST API that provides route planning functionality using the Openrouteservice (ORS) API. This service accepts start and end coordinates and returns detailed routing information including ETA, distance, and turn-by-turn directions.

## Features

- **Route Calculation**: Get routes between two coordinates
- **Multiple Transportation Modes**: Support for driving, walking, cycling, etc.
- **Detailed Directions**: Turn-by-turn navigation instructions
- **ETA Calculation**: Estimated time of arrival
- **Distance Information**: Route distance in kilometers
- **Geometry Data**: Route coordinates for mapping

## Prerequisites

- Java 21
- Gradle
- Openrouteservice API Key (free at https://openrouteservice.org/)

## Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd OSM-POC
   ```

2. **Configure API Key**
   
   Update `src/main/resources/application.properties`:
   ```properties
   ors.api.key=YOUR_ACTUAL_ORS_API_KEY_HERE
   ```
   
   Get your free API key from: https://openrouteservice.org/dev/#/signup

3. **Build the application**
   ```bash
   ./gradlew build
   ```

4. **Run the application**
   ```bash
   ./gradlew bootRun
   ```

The API will be available at `http://localhost:8080`

## API Endpoints

### Get Route Information

**GET** `/api/routes`

**Parameters:**
- `start_lat` (required): Starting latitude
- `start_lon` (required): Starting longitude
- `end_lat` (required): Ending latitude
- `end_lon` (required): Ending longitude
- `profile` (optional): Transportation profile (default: "driving-car")

**Supported Profiles:**
- `driving-car` (default)
- `driving-hgv` (heavy goods vehicle)
- `foot-walking`
- `foot-hiking`
- `cycling-regular`
- `cycling-road`
- `cycling-mountain`
- `cycling-electric`

**Example Request:**
```
GET /api/routes?start_lat=19.0760&start_lon=72.8777&end_lat=28.7041&end_lon=77.1025
```

**Example Response:**
```json
{
  "distanceKm": 1155.2,
  "durationMinutes": 1320.5,
  "eta": "2024-01-01 22:00:30",
  "directions": [
    {
      "instruction": "Head north on Dr Dadabhai Naoroji Rd",
      "distanceKm": 0.5,
      "durationMinutes": 2.1,
      "type": "straight",
      "exitNumber": 0
    }
  ],
  "geometry": {
    "type": "LineString",
    "coordinates": [
      [72.8777, 19.0760],
      [72.8780, 19.0765]
    ]
  }
}
```

### Health Check

**GET** `/api/routes/health`

Returns: `"Route service is running"`

## Test Data

The file `indian_cities_coordinates.json` contains coordinates for major Indian cities and sample API requests for testing.

### Sample Test Requests:

1. **Mumbai to Delhi:**
   ```
   GET /api/routes?start_lat=19.0760&start_lon=72.8777&end_lat=28.7041&end_lon=77.1025
   ```

2. **Bangalore to Chennai:**
   ```
   GET /api/routes?start_lat=12.9716&start_lon=77.5946&end_lat=13.0827&end_lon=80.2707
   ```

3. **Delhi to Jaipur:**
   ```
   GET /api/routes?start_lat=28.7041&start_lon=77.1025&end_lat=26.9124&end_lon=75.7873
   ```

4. **Cycling Route (Hyderabad to Bangalore):**
   ```
   GET /api/routes?start_lat=17.3850&start_lon=78.4867&end_lat=12.9716&end_lon=77.5946&profile=cycling-regular
   ```

## Testing

Run the tests:
```bash
./gradlew test
```

## Project Structure

```
src/main/java/com/example/osm/OSM/POC/
├── OsmPocApplication.java          # Main application class
├── config/
│   ├── OrsProperties.java          # ORS API configuration
│   └── WebClientConfig.java        # HTTP client configuration
├── controller/
│   └── RouteController.java        # REST API endpoints
├── dto/
│   ├── RouteRequest.java           # Request DTO
│   ├── RouteResponse.java          # Response DTO
│   └── ors/
│       └── OrsResponse.java        # ORS API response DTO
└── service/
    └── RouteService.java           # Business logic
```

## Error Handling

The API returns appropriate HTTP status codes:
- `200 OK`: Successful route calculation
- `400 Bad Request`: Invalid parameters or service errors
- `500 Internal Server Error`: Unexpected server errors

## Rate Limits

Free ORS API accounts have rate limits:
- 2000 requests per day
- 40 requests per minute

For production use, consider upgrading to a paid plan.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is for demonstration purposes only.
