package com.example.osm.OSM.POC.controller;

import com.example.osm.OSM.POC.dto.RouteResponse;
import com.example.osm.OSM.POC.service.RouteService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(RouteController.class)
class RouteControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private RouteService routeService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testGetRoute_Success() throws Exception {
        // Arrange
        RouteResponse mockResponse = new RouteResponse();
        mockResponse.setDistanceKm(150.5);
        mockResponse.setDurationMinutes(180.0);
        mockResponse.setEta("2024-01-01 15:30:00");
        mockResponse.setDirections(Arrays.asList(
                new RouteResponse.Direction("Head north", 1.2, 2.0, "straight", 0)
        ));
        mockResponse.setGeometry(new RouteResponse.RouteGeometry("LineString", 
                Arrays.asList(Arrays.asList(77.1025, 28.7041), Arrays.asList(77.2025, 28.8041))));

        when(routeService.getRoute(any())).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/api/routes")
                .param("start_lat", "28.7041")
                .param("start_lon", "77.1025")
                .param("end_lat", "26.9124")
                .param("end_lon", "75.7873"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.distanceKm").value(150.5))
                .andExpect(jsonPath("$.durationMinutes").value(180.0))
                .andExpect(jsonPath("$.eta").value("2024-01-01 15:30:00"));
    }

    @Test
    void testGetRoute_WithProfile() throws Exception {
        // Arrange
        RouteResponse mockResponse = new RouteResponse();
        mockResponse.setDistanceKm(120.0);
        mockResponse.setDurationMinutes(300.0);
        mockResponse.setEta("2024-01-01 17:00:00");

        when(routeService.getRoute(any())).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/api/routes")
                .param("start_lat", "19.0760")
                .param("start_lon", "72.8777")
                .param("end_lat", "18.5204")
                .param("end_lon", "73.8567")
                .param("profile", "cycling-regular"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.distanceKm").value(120.0))
                .andExpect(jsonPath("$.durationMinutes").value(300.0));
    }

    @Test
    void testHealthEndpoint() throws Exception {
        mockMvc.perform(get("/api/routes/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("Route service is running"));
    }

    @Test
    void testGetRoute_ServiceException() throws Exception {
        // Arrange
        when(routeService.getRoute(any())).thenThrow(new RuntimeException("Service error"));

        // Act & Assert
        mockMvc.perform(get("/api/routes")
                .param("start_lat", "28.7041")
                .param("start_lon", "77.1025")
                .param("end_lat", "26.9124")
                .param("end_lon", "75.7873"))
                .andExpect(status().isBadRequest());
    }
}
