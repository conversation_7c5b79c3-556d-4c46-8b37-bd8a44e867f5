package com.example.osm.OSM.POC;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication
@EnableConfigurationProperties
@EnableFeignClients
public class OsmPocApplication {

	public static void main(String[] args) {
		SpringApplication.run(OsmPocApplication.class, args);
	}

}
