package com.example.osm.OSM.POC.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for our API response containing routing information
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RouteResponse {
    private double distanceKm;
    private double durationMinutes;
    private String eta;
    private List<Direction> directions;
    private RouteGeometry geometry;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Direction {
        private String instruction;
        private double distanceKm;
        private double durationMinutes;
        private String type;
        private int exitNumber;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RouteGeometry {
        private String type;
        private List<List<Double>> coordinates;
    }
}
