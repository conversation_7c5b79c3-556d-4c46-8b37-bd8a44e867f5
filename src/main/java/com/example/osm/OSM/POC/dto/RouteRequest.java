package com.example.osm.OSM.POC.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for route request parameters
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RouteRequest {
    private double startLongitude;
    private double startLatitude;
    private double endLongitude;
    private double endLatitude;
    private String profile; // driving-car, foot-walking, cycling-regular, etc.
    
    public RouteRequest(double startLongitude, double startLatitude, double endLongitude, double endLatitude) {
        this.startLongitude = startLongitude;
        this.startLatitude = startLatitude;
        this.endLongitude = endLongitude;
        this.endLatitude = endLatitude;
        this.profile = "driving-car"; // default profile
    }
}
