package com.example.osm.OSM.POC.controller;

import com.example.osm.OSM.POC.dto.RouteRequest;
import com.example.osm.OSM.POC.dto.RouteResponse;
import com.example.osm.OSM.POC.service.RouteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST Controller for route operations
 */
@Slf4j
@RestController
@RequestMapping("/api/routes")
@RequiredArgsConstructor
public class RouteController {
    
    private final RouteService routeService;
    
    /**
     * Get route information between two coordinates
     * 
     * @param startLat Start latitude
     * @param startLon Start longitude  
     * @param endLat End latitude
     * @param endLon End longitude
     * @param profile Transportation profile (optional, defaults to driving-car)
     * @return Route information including ETA, distance, and directions
     */
    @GetMapping
    public ResponseEntity<RouteResponse> getRoute(
            @RequestParam("start_lat") double startLat,
            @RequestParam("start_lon") double startLon,
            @RequestParam("end_lat") double endLat,
            @RequestParam("end_lon") double endLon,
            @RequestParam(value = "profile", defaultValue = "driving-car") String profile) {
        
        log.info("Received route request: start=({}, {}), end=({}, {}), profile={}", 
                startLat, startLon, endLat, endLon, profile);
        
        try {
            RouteRequest request = new RouteRequest(startLon, startLat, endLon, endLat, profile);
            RouteResponse response = routeService.getRoute(request);
            
            log.info("Route calculated successfully: distance={}km, duration={}min", 
                    response.getDistanceKm(), response.getDurationMinutes());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error processing route request", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("Route service is running");
    }
}
