package com.example.osm.OSM.POC.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import com.example.osm.OSM.POC.dto.ors.OrsResponse;

/**
 * Feign client for Openrouteservice API
 */
@FeignClient(
    name = "ors-client",
    url = "${ors.api.base-url}"
)
public interface OrsClient {
    
    /**
     * Get directions from ORS API
     * 
     * @param profile Transportation profile (driving-car, foot-walking, etc.)
     * @param apiKey API key for authentication
     * @param start Start coordinates in format "longitude,latitude"
     * @param end End coordinates in format "longitude,latitude"
     * @return ORS API response
     */
    @GetMapping("/v2/directions/{profile}")
    OrsResponse getDirections(
            @PathVariable("profile") String profile,
            @RequestParam("api_key") String apiKey,
            @RequestParam("start") String start,
            @RequestParam("end") String end
    );
}
