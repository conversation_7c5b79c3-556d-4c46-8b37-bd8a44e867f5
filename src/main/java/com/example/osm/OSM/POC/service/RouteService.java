package com.example.osm.OSM.POC.service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.example.osm.OSM.POC.client.OrsClient;
import com.example.osm.OSM.POC.config.OrsProperties;
import com.example.osm.OSM.POC.dto.RouteRequest;
import com.example.osm.OSM.POC.dto.RouteResponse;
import com.example.osm.OSM.POC.dto.ors.OrsResponse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Service for handling route calculations using Openrouteservice API
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RouteService {

    private final OrsClient orsClient;
    private final OrsProperties orsProperties;
    
    /**
     * Get route information from start to end coordinates
     */
    public RouteResponse getRoute(RouteRequest request) {
        try {
            log.info("Requesting route from ({}, {}) to ({}, {})", 
                    request.getStartLatitude(), request.getStartLongitude(),
                    request.getEndLatitude(), request.getEndLongitude());
            
            OrsResponse orsResponse = callOrsApi(request);
            return convertToRouteResponse(orsResponse);
            
        } catch (Exception e) {
            log.error("Unexpected error getting route", e);
            throw new RuntimeException("Failed to get route information: " + e.getMessage());
        }
    }
    
    private OrsResponse callOrsApi(RouteRequest request) {
        String start = String.format("%f,%f",
                request.getStartLongitude(), request.getStartLatitude());
        String end = String.format("%f,%f",
                request.getEndLongitude(), request.getEndLatitude());

        log.info("Calling ORS API with start={}, end={}, profile={}", start, end, request.getProfile());

        OrsResponse response = orsClient.getDirections(
                request.getProfile(),
                orsProperties.getKey(),
                start,
                end
        );

        log.info("ORS API response: features count = {}",
                response != null && response.getFeatures() != null ? response.getFeatures().size() : "null");

        return response;
    }
    
    private RouteResponse convertToRouteResponse(OrsResponse orsResponse) {
        if (orsResponse.getFeatures() == null || orsResponse.getFeatures().isEmpty()) {
            throw new RuntimeException("No routes found");
        }

        OrsResponse.Feature feature = orsResponse.getFeatures().get(0);
        OrsResponse.Summary summary = feature.getProperties().getSummary();
        
        // Convert distance from meters to kilometers
        double distanceKm = summary.getDistance() / 1000.0;
        
        // Convert duration from seconds to minutes
        double durationMinutes = summary.getDuration() / 60.0;
        
        // Calculate ETA
        String eta = LocalDateTime.now()
                .plusSeconds((long) summary.getDuration())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        // Convert directions
        List<RouteResponse.Direction> directions = convertDirections(feature);

        // Convert geometry
        RouteResponse.RouteGeometry geometry = new RouteResponse.RouteGeometry(
                feature.getGeometry().getType(),
                feature.getGeometry().getCoordinates()
        );
        
        return new RouteResponse(distanceKm, durationMinutes, eta, directions, geometry);
    }
    
    private List<RouteResponse.Direction> convertDirections(OrsResponse.Feature feature) {
        return feature.getProperties().getSegments().stream()
                .flatMap(segment -> segment.getSteps().stream())
                .map(step -> new RouteResponse.Direction(
                        step.getInstruction(),
                        step.getDistance() / 1000.0, // Convert to km
                        step.getDuration() / 60.0,   // Convert to minutes
                        getStepType(step.getType()),
                        step.getExitNumber()
                ))
                .collect(Collectors.toList());
    }
    
    private String getStepType(int type) {
        // ORS step types mapping
        return switch (type) {
            case 0 -> "left";
            case 1 -> "right";
            case 2 -> "sharp_left";
            case 3 -> "sharp_right";
            case 4 -> "slight_left";
            case 5 -> "slight_right";
            case 6 -> "straight";
            case 7 -> "enter_roundabout";
            case 8 -> "exit_roundabout";
            case 9 -> "u_turn";
            case 10 -> "goal";
            case 11 -> "depart";
            case 12 -> "keep_left";
            case 13 -> "keep_right";
            default -> "unknown";
        };
    }
}
