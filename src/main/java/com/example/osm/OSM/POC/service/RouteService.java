package com.example.osm.OSM.POC.service;

import com.example.osm.OSM.POC.config.OrsProperties;
import com.example.osm.OSM.POC.dto.RouteRequest;
import com.example.osm.OSM.POC.dto.RouteResponse;
import com.example.osm.OSM.POC.dto.ors.OrsResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for handling route calculations using Openrouteservice API
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RouteService {
    
    private final WebClient webClient;
    private final OrsProperties orsProperties;
    
    /**
     * Get route information from start to end coordinates
     */
    public RouteResponse getRoute(RouteRequest request) {
        try {
            log.info("Requesting route from ({}, {}) to ({}, {})", 
                    request.getStartLatitude(), request.getStartLongitude(),
                    request.getEndLatitude(), request.getEndLongitude());
            
            OrsResponse orsResponse = callOrsApi(request);
            return convertToRouteResponse(orsResponse);
            
        } catch (WebClientResponseException e) {
            log.error("Error calling ORS API: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("Failed to get route information: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error getting route", e);
            throw new RuntimeException("Failed to get route information: " + e.getMessage());
        }
    }
    
    private OrsResponse callOrsApi(RouteRequest request) {
        String url = String.format("%s/v2/directions/%s", 
                orsProperties.getBaseUrl(), 
                request.getProfile());
        
        String coordinates = String.format("%f,%f|%f,%f",
                request.getStartLongitude(), request.getStartLatitude(),
                request.getEndLongitude(), request.getEndLatitude());
        
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path(url)
                        .queryParam("api_key", orsProperties.getKey())
                        .queryParam("coordinates", coordinates)
                        .queryParam("format", "json")
                        .queryParam("instructions", "true")
                        .queryParam("geometry", "true")
                        .build())
                .retrieve()
                .bodyToMono(OrsResponse.class)
                .block();
    }
    
    private RouteResponse convertToRouteResponse(OrsResponse orsResponse) {
        if (orsResponse.getRoutes() == null || orsResponse.getRoutes().isEmpty()) {
            throw new RuntimeException("No routes found");
        }
        
        OrsResponse.Route route = orsResponse.getRoutes().get(0);
        OrsResponse.Summary summary = route.getSummary();
        
        // Convert distance from meters to kilometers
        double distanceKm = summary.getDistance() / 1000.0;
        
        // Convert duration from seconds to minutes
        double durationMinutes = summary.getDuration() / 60.0;
        
        // Calculate ETA
        String eta = LocalDateTime.now()
                .plusSeconds((long) summary.getDuration())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        // Convert directions
        List<RouteResponse.Direction> directions = convertDirections(route);
        
        // Convert geometry
        RouteResponse.RouteGeometry geometry = new RouteResponse.RouteGeometry(
                route.getGeometry().getType(),
                route.getGeometry().getCoordinates()
        );
        
        return new RouteResponse(distanceKm, durationMinutes, eta, directions, geometry);
    }
    
    private List<RouteResponse.Direction> convertDirections(OrsResponse.Route route) {
        return route.getSegments().stream()
                .flatMap(segment -> segment.getSteps().stream())
                .map(step -> new RouteResponse.Direction(
                        step.getInstruction(),
                        step.getDistance() / 1000.0, // Convert to km
                        step.getDuration() / 60.0,   // Convert to minutes
                        getStepType(step.getType()),
                        step.getExitNumber()
                ))
                .collect(Collectors.toList());
    }
    
    private String getStepType(int type) {
        // ORS step types mapping
        return switch (type) {
            case 0 -> "left";
            case 1 -> "right";
            case 2 -> "sharp_left";
            case 3 -> "sharp_right";
            case 4 -> "slight_left";
            case 5 -> "slight_right";
            case 6 -> "straight";
            case 7 -> "enter_roundabout";
            case 8 -> "exit_roundabout";
            case 9 -> "u_turn";
            case 10 -> "goal";
            case 11 -> "depart";
            case 12 -> "keep_left";
            case 13 -> "keep_right";
            default -> "unknown";
        };
    }
}
