package com.example.osm.OSM.POC.dto.ors;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for Openrouteservice API response
 */
@Data
@NoArgsConstructor
public class OrsResponse {
    private String type;
    private List<Double> bbox;
    private List<Feature> features;
    private Metadata metadata;
    
    @Data
    @NoArgsConstructor
    public static class Feature {
        private String type;
        private List<Double> bbox;
        private Properties properties;
        private Geometry geometry;
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private List<Segment> segments;
        private Summary summary;
        private List<Integer> wayPoints;
    }
    
    @Data
    @NoArgsConstructor
    public static class Summary {
        private double distance;
        private double duration;
    }
    
    @Data
    @NoArgsConstructor
    public static class Geometry {
        private String type;
        private List<List<Double>> coordinates;
    }
    
    @Data
    @NoArgsConstructor
    public static class Segment {
        private double distance;
        private double duration;
        private List<Step> steps;
    }
    
    @Data
    @NoArgsConstructor
    public static class Step {
        private double distance;
        private double duration;
        private int type;
        private String instruction;
        private String name;
        private List<Integer> wayPoints;
        private int exitNumber;
    }
    
    @Data
    @NoArgsConstructor
    public static class Metadata {
        private String attribution;
        private String service;
        private long timestamp;
        private Query query;
        private Engine engine;
    }
    
    @Data
    @NoArgsConstructor
    public static class Query {
        private List<List<Double>> coordinates;
        private String profile;
        private String format;
    }
    
    @Data
    @NoArgsConstructor
    public static class Engine {
        private String version;
        private String buildDate;
        private String graphDate;
    }
}
